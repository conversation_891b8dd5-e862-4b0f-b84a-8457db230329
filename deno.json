{"lock": false, "tasks": {"check": "deno task format:check && deno lint && deno check **/*.ts && deno check **/*.tsx", "cli": "echo \"import '\\$fresh/src/dev/cli.ts'\" | deno run --unstable -A -", "manifest": "deno task cli manifest $(pwd)", "start": "deno run -A --unstable-kv --watch=static/,routes/,layouts/,components/,islands/,hooks/ dev.ts", "build": "deno run -A --unstable-kv dev.ts build", "preview": "deno run -A --unstable-kv main.ts", "update": "deno run -A -r https://fresh.deno.dev/update .", "format": "deno run -A npm:@biomejs/biome format --write .", "format:check": "deno run -A npm:@biomejs/biome format --check ."}, "lint": {"rules": {"tags": ["fresh", "recommended"]}}, "exclude": ["**/_fresh/*"], "imports": {"$fresh/": "https://deno.land/x/fresh@1.7.3/", "@biomejs/biome": "npm:@biomejs/biome@^1.9.4", "preact": "https://esm.sh/preact@10.22.0", "preact/": "https://esm.sh/preact@10.22.0/", "@preact/signals": "https://esm.sh/*@preact/signals@1.2.2", "@preact/signals-core": "https://esm.sh/*@preact/signals-core@1.5.1", "tailwindcss": "npm:tailwindcss@3.4.1", "tailwindcss/": "npm:/tailwindcss@3.4.1/", "tailwindcss/plugin": "npm:/tailwindcss@3.4.1/plugin.js", "$std/": "https://deno.land/std@0.216.0/", "@/": "./", "zod": "https://deno.land/x/zod@v3.22.4/mod.ts"}, "compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "preact"}, "nodeModulesDir": "auto"}