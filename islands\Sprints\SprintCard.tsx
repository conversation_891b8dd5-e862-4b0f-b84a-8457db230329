import { useState } from "preact/hooks";
import { Button } from "../../components/Button.tsx";
import { SprintStatus } from "../../models/sprint.ts";
import type { Sprint } from "../../models/sprint.ts";
import { deleteSprint } from "../../services/sprintService.ts";
import Modal from "../Modal.tsx";
import EditSprintForm from "./EditSprintForm.tsx";

interface SprintCardProps {
  sprint: Sprint;
  onUpdate: () => void;
  canManage: boolean;
}

export default function SprintCard({ sprint, onUpdate, canManage }: SprintCardProps) {
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Formatear fechas
  const formatDate = (timestamp?: number) => {
    if (!timestamp) return "No definida";
    return new Date(timestamp).toLocaleDateString();
  };

  // Obtener color según el estado
  const getStatusColor = (status: SprintStatus) => {
    switch (status) {
      case SprintStatus.PLANNED:
        return "bg-blue-100 text-blue-800";
      case SprintStatus.ACTIVE:
        return "bg-green-100 text-green-800";
      case SprintStatus.COMPLETED:
        return "bg-purple-100 text-purple-800";
      case SprintStatus.CANCELLED:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Obtener texto del estado
  const getStatusText = (status: SprintStatus) => {
    switch (status) {
      case SprintStatus.PLANNED:
        return "Planificado";
      case SprintStatus.ACTIVE:
        return "Activo";
      case SprintStatus.COMPLETED:
        return "Completado";
      case SprintStatus.CANCELLED:
        return "Cancelado";
      default:
        return status;
    }
  };

  // Función para eliminar el sprint
  const handleDeleteSprint = async () => {
    setIsDeleting(true);
    setError(null);

    try {
      await deleteSprint(sprint.id);
      onUpdate();
      setShowDeleteConfirmModal(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Error al eliminar el sprint");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div class="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
      <div class="p-4">
        <div class="flex justify-between items-start">
          <h3 class="text-lg font-semibold text-gray-800">{sprint.name}</h3>
          {canManage && (
            <div class="flex space-x-2">
              <button
                type="button"
                onClick={() => setShowEditModal(true)}
                class="text-blue-600 hover:text-blue-800"
                title="Editar Sprint"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-labelledby="editSprintTitle"
                  role="img"
                >
                  <title id="editSprintTitle">Editar Sprint</title>
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
              </button>
              <button
                type="button"
                onClick={() => setShowDeleteConfirmModal(true)}
                class="text-red-600 hover:text-red-800"
                title="Eliminar Sprint"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-labelledby="deleteSprintTitle"
                  role="img"
                >
                  <title id="deleteSprintTitle">Eliminar Sprint</title>
                  <path
                    fill-rule="evenodd"
                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
            </div>
          )}
        </div>

        <div class="mt-2">
          <span
            class={`inline-block px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(sprint.status)}`}
          >
            {getStatusText(sprint.status)}
          </span>
        </div>

        {sprint.goal && (
          <div class="mt-3">
            <p class="text-sm text-gray-600">{sprint.goal}</p>
          </div>
        )}

        <div class="mt-4 grid grid-cols-2 gap-2 text-sm">
          <div>
            <span class="text-gray-500">Inicio:</span>
            <p class="font-medium">{formatDate(sprint.startDate)}</p>
          </div>
          <div>
            <span class="text-gray-500">Fin:</span>
            <p class="font-medium">{formatDate(sprint.endDate)}</p>
          </div>
        </div>

        <div class="mt-4">
          <span class="text-gray-500 text-sm">Historias de usuario:</span>
          <p class="font-medium">{sprint.userStoryIds.length}</p>
        </div>

        <div class="mt-4 flex justify-end">
          <Button
            onClick={() => {
              globalThis.location.href = `/sprints/${sprint.id}`;
            }}
            class="bg-blue-600 hover:bg-blue-700 text-white text-sm"
          >
            Ver detalles
          </Button>
        </div>
      </div>

      {/* Modal para editar sprint */}
      <Modal show={showEditModal} onClose={() => setShowEditModal(false)}>
        <div class="p-4">
          <h2 class="text-xl font-semibold mb-4">Editar Sprint</h2>
          <EditSprintForm
            sprint={sprint}
            onSuccess={() => {
              onUpdate();
              setShowEditModal(false);
            }}
            onCancel={() => setShowEditModal(false)}
          />
        </div>
      </Modal>

      {/* Modal para confirmar eliminación */}
      <Modal show={showDeleteConfirmModal} onClose={() => setShowDeleteConfirmModal(false)}>
        <div class="p-4">
          <h2 class="text-xl font-semibold mb-4">Confirmar eliminación</h2>
          {error && (
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              <p>{error}</p>
            </div>
          )}
          <p class="mb-4">¿Estás seguro de que deseas eliminar este sprint?</p>
          <div class="flex justify-end space-x-2">
            <Button
              onClick={() => setShowDeleteConfirmModal(false)}
              class="bg-gray-300 hover:bg-gray-400 text-gray-800"
              disabled={isDeleting}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleDeleteSprint}
              class="bg-red-600 hover:bg-red-700 text-white"
              disabled={isDeleting}
            >
              {isDeleting ? "Eliminando..." : "Eliminar"}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
