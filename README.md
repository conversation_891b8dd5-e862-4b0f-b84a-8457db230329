# WorkflowS - Plataforma de Gestión de Proyectos Scrum

WorkflowS es una plataforma moderna de gestión de proyectos diseñada específicamente para equipos que utilizan la metodología Scrum. Nuestra plataforma facilita la colaboración, el seguimiento del progreso y la gestión eficiente de proyectos ágiles.

## Características Principales

- **Gestión de Proyectos**: Crea y administra múltiples proyectos Scrum
- **Roles Específicos**: Funcionalidades adaptadas para Product Owners, Scrum Masters y Desarrolladores
- **Historias de Usuario**: Crea, prioriza y gestiona historias de usuario
- **Sprints**: Planifica y realiza seguimiento de sprints
- **Backlog**: Administra el backlog del producto
- **Colaboración**: Facilita la comunicación entre los miembros del equipo

## Tecnologías Utilizadas

- **Deno**: Un entorno de ejecución seguro para JavaScript y TypeScript
- **Fresh**: Un framework web de última generación para Deno
- **Preact**: Una alternativa ligera a React con la misma API
- **Tailwind CSS**: Un framework CSS basado en utilidades
- **Deno KV**: Base de datos de clave-valor para almacenamiento persistente

## Instalación y Uso

1. Asegúrate de tener Deno instalado: https://deno.land/manual/getting_started/installation

2. Clona este repositorio:
   ```
   git clone https://github.com/tu-usuario/workflows.git
   cd workflows
   ```

3. Inicia el proyecto:
   ```
   deno task start
   ```

4. Abre tu navegador en `http://localhost:8000`

## Estructura del Proyecto

- **layouts/**: Componentes de diseño
- **components/**: Componentes de UI reutilizables
- **islands/**: Componentes interactivos
- **routes/**: Páginas y endpoints de API
- **models/**: Modelos de datos y lógica de negocio
- **utils/**: Utilidades y funciones auxiliares
