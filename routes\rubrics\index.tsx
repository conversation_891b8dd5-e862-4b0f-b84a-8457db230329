import type { FreshContext, PageProps } from "$fresh/server.ts";
import { MainLayout } from "../../layouts/MainLayout.tsx";
import { UserRole } from "../../models/user.ts";
import { getSession } from "../../utils/session.ts";

interface Data {
  session: {
    userId: string;
    username: string;
    email: string;
    role: UserRole;
  };
}

export const handler = {
  async GET(req: Request, ctx: FreshContext) {
    const session = await getSession(req);

    if (!session) {
      return new Response("", {
        status: 302,
        headers: {
          Location: `/login?redirect=${encodeURIComponent(req.url)}`,
        },
      });
    }

    // Solo los profesores pueden acceder a la gestión de rúbricas
    if (
      session.role !== UserRole.ADMIN &&
      session.role !== UserRole.PRODUCT_OWNER &&
      session.role !== UserRole.SCRUM_MASTER
    ) {
      return new Response("", {
        status: 302,
        headers: {
          Location: "/unauthorized",
        },
      });
    }

    return ctx.render({ session });
  },
};

export default function RubricsPage({ data }: PageProps<Data>) {
  const { session } = data;

  return (
    <MainLayout title="Gestión de Rúbricas - WorkflowS" session={session}>
      <div class="container mx-auto px-4 py-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">Gestión de Rúbricas</h1>

        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="text-center">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Sistema de Rúbricas</h2>
            <p class="text-gray-600 mb-6">
              Gestiona las rúbricas de evaluación para tus proyectos académicos.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div class="bg-blue-50 p-4 rounded-lg">
                <h3 class="font-semibold text-blue-800 mb-2">Crear Rúbrica</h3>
                <p class="text-blue-600 text-sm mb-3">Crea una nueva rúbrica de evaluación</p>
                <a
                  href="/rubrics/create"
                  class="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                >
                  Crear Nueva
                </a>
              </div>

              <div class="bg-green-50 p-4 rounded-lg">
                <h3 class="font-semibold text-green-800 mb-2">Plantillas</h3>
                <p class="text-green-600 text-sm mb-3">Explora plantillas predefinidas</p>
                <a
                  href="/rubrics/create?template=true"
                  class="inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                >
                  Ver Plantillas
                </a>
              </div>

              <div class="bg-purple-50 p-4 rounded-lg">
                <h3 class="font-semibold text-purple-800 mb-2">Mis Rúbricas</h3>
                <p class="text-purple-600 text-sm mb-3">Gestiona tus rúbricas existentes</p>
                <button
                  class="inline-block bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
                  onclick="alert('Funcionalidad en desarrollo')"
                >
                  Ver Lista
                </button>
              </div>
            </div>

            <div class="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <div>
                  <h4 class="text-yellow-800 font-medium">Página Simplificada</h4>
                  <p class="text-yellow-700 text-sm">
                    Esta es una versión simplificada para diagnosticar problemas.
                    La funcionalidad completa se restaurará una vez resueltos los errores.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
