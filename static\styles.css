@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */

/* Apply Ubuntu Sans to specific elements if needed */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Ubuntu Sans", ui-sans-serif, system-ui, sans-serif;
  font-weight: 700;
}

/* Custom font weights for Ubuntu Sans */
.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* Smooth scrolling and better text rendering */
html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Material Symbols configuration */
.material-symbols-outlined {
  font-family: "Material Symbols Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px; /* Preferred icon size */
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  vertical-align: middle;
}

/* Sidebar collapsed state - ocultar texto en modo icono */
.group[data-collapsible="icon"][data-state="collapsed"]
  [data-sidebar="menu-button"]
  > span:last-child {
  opacity: 0 !important;
  width: 0 !important;
  overflow: hidden;
  transition: opacity 200ms ease-linear, width 200ms ease-linear;
}

.group[data-collapsible="icon"][data-state="collapsed"] [data-sidebar="group-label"] {
  opacity: 0 !important;
  margin-top: -2rem !important;
  transition: opacity 200ms ease-linear, margin-top 200ms ease-linear;
}

/* Asegurar que los iconos se mantengan visibles */
.group[data-collapsible="icon"] [data-sidebar="menu-button"] > svg {
  opacity: 1 !important;
  flex-shrink: 0;
}

/* Debug: Mostrar el estado del sidebar - descomentado para debug */
/*
.group[data-state="collapsed"] {
  border: 2px solid red !important;
}

.group[data-state="expanded"] {
  border: 2px solid green !important;
}
*/

/* Forzar el ancho del sidebar en estado collapsed */
.group[data-state="collapsed"][data-collapsible="icon"]
  .group-data-\[collapsible\=icon\]\:w-\[var\(--sidebar-width-icon\)\] {
  width: var(--sidebar-width-icon) !important;
}
